import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { authenticateAdmin } from '../../../../../lib/supabaseAuthUtils.js';
import { addAppointment, isTimeSlotAvailable } from '../../../../../lib/supabaseAppointmentUtils.js';
import { isWeekday, formatDate } from '../../../../../lib/utils.js';

// Configure your email settings here
const CAF_EMAIL = process.env.CAF_EMAIL || '<EMAIL>';
const EMAIL_USER = process.env.EMAIL_USER || '<EMAIL>';
const EMAIL_PASS = process.env.EMAIL_PASS || 'jpxjlwdlvfkceqgi';

export async function POST(request) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const data = await request.json();
    const { nome, cognome, telefono, email, servizio, prestazione, dataAppuntamento, orario } = data;

    // Validate required fields
    if (!nome || !cognome || !telefono || !email || !servizio || !dataAppuntamento || !orario) {
      return NextResponse.json(
        { success: false, message: 'Tutti i campi sono obbligatori' },
        { status: 400 }
      );
    }

    // Validate that the date is a weekday
    if (!isWeekday(dataAppuntamento)) {
      return NextResponse.json(
        { success: false, message: 'Gli appuntamenti sono disponibili solo dal lunedì al venerdì' },
        { status: 400 }
      );
    }

    // Check if the date is in the past
    const selectedDate = new Date(dataAppuntamento);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      return NextResponse.json(
        { success: false, message: 'Non è possibile prenotare appuntamenti per date passate' },
        { status: 400 }
      );
    }

    // Check if the time slot is available
    if (!(await isTimeSlotAvailable(dataAppuntamento, orario))) {
      return NextResponse.json(
        {
          success: false,
          message: 'L\'orario selezionato non è più disponibile. Ricarica la pagina e scegli un altro orario.',
          code: 'TIME_SLOT_UNAVAILABLE'
        },
        { status: 409 } // Conflict status code
      );
    }

    // Create email transporter
    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: EMAIL_USER,
        pass: EMAIL_PASS,
      },
    });

    // Email content for user
    const userEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #B42C2C; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">CAF Monte Sacro</h1>
          <h2 style="margin: 10px 0 0 0;">Conferma Appuntamento</h2>
        </div>
        
        <div style="padding: 20px; background-color: #f9f9f9;">
          <h3 style="color: #B42C2C;">Gentile ${nome} ${cognome},</h3>
          <p>Il suo appuntamento è stato confermato con i seguenti dettagli:</p>
          
          <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <p><strong>📅 Data:</strong> ${formatDate(dataAppuntamento)}</p>
            <p><strong>🕐 Orario:</strong> ${orario}</p>
            <p><strong>🏢 Servizio:</strong> ${servizio}</p>
            ${prestazione ? `<p><strong>📋 Prestazione:</strong> ${prestazione}</p>` : ''}
            <p><strong>📧 Email:</strong> ${email}</p>
            <p><strong>📞 Telefono:</strong> ${telefono}</p>
          </div>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <p style="margin: 0;"><strong>⚠️ Importante:</strong> L'orario potrebbe subire lievi variazioni. Se al suo arrivo un altro cliente è già in fase di servizio, verrà chiamato subito dopo.</p>
          </div>
          
          <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h4 style="color: #B42C2C; margin-top: 0;">📍 Come raggiungerci:</h4>
            <p><strong>CAF Monte Sacro</strong><br>
            Via Nomentana, 123<br>
            00141 Roma<br>
            Tel: 06 123456789</p>
          </div>
          
          <p>Grazie per aver scelto i nostri servizi!</p>
          <p style="color: #666; font-size: 12px; margin-top: 20px;">
            Questa è una email automatica, si prega di non rispondere.
          </p>
        </div>
      </div>
    `;

    // Email content for CAF office
    const cafEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #252B59; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Nuovo Appuntamento</h1>
          <h2 style="margin: 10px 0 0 0;">Creato dall'Amministratore</h2>
        </div>
        
        <div style="padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
            <p style="margin: 0;"><strong>👤 Creato da:</strong> ${user.username} (Admin)</p>
          </div>
          
          <div style="background-color: white; padding: 15px; border-radius: 5px;">
            <h3 style="color: #252B59; margin-top: 0;">Dettagli Cliente:</h3>
            <p><strong>Nome:</strong> ${nome} ${cognome}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Telefono:</strong> ${telefono}</p>
            
            <h3 style="color: #252B59;">Dettagli Appuntamento:</h3>
            <p><strong>📅 Data:</strong> ${formatDate(dataAppuntamento)}</p>
            <p><strong>🕐 Orario:</strong> ${orario}</p>
            <p><strong>🏢 Servizio:</strong> ${servizio}</p>
            ${prestazione ? `<p><strong>📋 Prestazione:</strong> ${prestazione}</p>` : ''}
          </div>
        </div>
      </div>
    `;

    // Save appointment to Supabase
    const savedAppointment = await addAppointment(data);

    // Send email to user
    await transporter.sendMail({
      from: EMAIL_USER,
      to: email,
      subject: 'Conferma Appuntamento CAF',
      html: userEmailContent,
    });

    // Send email to CAF office
    await transporter.sendMail({
      from: EMAIL_USER,
      to: CAF_EMAIL,
      subject: `Nuovo Appuntamento (Admin) - ${nome} ${cognome} - ${formatDate(dataAppuntamento)} ${orario}`,
      html: cafEmailContent,
    });

    return NextResponse.json({
      success: true,
      message: 'Appuntamento creato con successo e email inviate',
      appointment: savedAppointment
    });

  } catch (error) {
    console.error('Error creating admin appointment:', error);
    
    // Handle specific error types
    if (error.message === 'TIME_SLOT_UNAVAILABLE') {
      return NextResponse.json(
        {
          success: false,
          message: 'L\'orario selezionato non è più disponibile.',
          code: 'TIME_SLOT_UNAVAILABLE'
        },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { success: false, message: 'Errore nella creazione dell\'appuntamento' },
      { status: 500 }
    );
  }
}
